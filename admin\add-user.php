<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('login.php');
}

$page_title = 'Add New User';

$errors = [];
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate input
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $date_of_birth = sanitizeInput($_POST['date_of_birth'] ?? '');
    $account_type = sanitizeInput($_POST['account_type'] ?? 'savings');
    $initial_balance = floatval($_POST['initial_balance'] ?? 0);
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $kyc_status = sanitizeInput($_POST['kyc_status'] ?? 'pending');
    $status = sanitizeInput($_POST['status'] ?? 'active');
    
    // Validation
    if (empty($username)) {
        $errors[] = 'Username is required.';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Username must be at least 3 characters long.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (empty($first_name)) {
        $errors[] = 'First name is required.';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Last name is required.';
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long.';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }
    
    if (!empty($date_of_birth)) {
        $dob = DateTime::createFromFormat('Y-m-d', $date_of_birth);
        if (!$dob || $dob->format('Y-m-d') !== $date_of_birth) {
            $errors[] = 'Please enter a valid date of birth.';
        }
    }
    
    if ($initial_balance < 0) {
        $errors[] = 'Initial balance cannot be negative.';
    }
    
    // Check for existing username and email
    if (empty($errors)) {
        try {
            $db = getDB();
            
            // Check username
            $check_username = $db->query("SELECT id FROM accounts WHERE username = ?", [$username]);
            if ($check_username->num_rows > 0) {
                $errors[] = 'Username already exists.';
            }
            
            // Check email
            $check_email = $db->query("SELECT id FROM accounts WHERE email = ?", [$email]);
            if ($check_email->num_rows > 0) {
                $errors[] = 'Email address already exists.';
            }
            
        } catch (Exception $e) {
            $errors[] = 'Database error occurred. Please try again.';
            error_log("Add user validation error: " . $e->getMessage());
        }
    }
    
    // Create user if no errors
    if (empty($errors)) {
        try {
            $db = getDB();
            $db->beginTransaction();
            
            // Generate account number
            $account_number = generateAccountNumber();
            
            // Hash password
            $hashed_password = hashPassword($password);
            
            // Insert user
            $sql = "INSERT INTO accounts (
                        account_number, username, password, email, first_name, last_name, 
                        phone, address, date_of_birth, account_type, balance, status, 
                        kyc_status, is_admin
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)";
            
            $params = [
                $account_number, $username, $hashed_password, $email, $first_name, $last_name,
                $phone, $address, $date_of_birth ?: null, $account_type, $initial_balance, $status,
                $kyc_status
            ];
            
            $user_id = $db->insert($sql, $params);
            
            // Log the activity
            logActivity($_SESSION['user_id'], 'Admin created new user', 'accounts', $user_id, null, [
                'username' => $username,
                'email' => $email,
                'account_number' => $account_number
            ]);
            
            $db->commit();
            
            $success = "User created successfully! Account Number: $account_number";
            
            // Clear form data
            $username = $email = $first_name = $last_name = $phone = $address = $date_of_birth = '';
            $initial_balance = 0;
            $account_type = 'savings';
            $kyc_status = 'pending';
            $status = 'active';
            
        } catch (Exception $e) {
            $db->rollback();
            $errors[] = 'Failed to create user. Please try again.';
            error_log("Add user error: " . $e->getMessage());
        }
    }
}

include '../includes/header.php';
?>

<?php include '../includes/sidebar.php'; ?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
                            <li class="breadcrumb-item"><a href="users.php">Users</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Add User</li>
                        </ol>
                    </nav>
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Add New User
                    </h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">User Information</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <line x1="15" y1="9" x2="9" y2="15"/>
                                            <line x1="9" y1="9" x2="15" y2="15"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="alert-title">Error!</h4>
                                        <ul class="mb-0">
                                            <?php foreach ($errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($success)): ?>
                            <div class="alert alert-success" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M5 12l5 5l10 -10"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="alert-title">Success!</h4>
                                        <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Username</label>
                                            <input type="text" name="username" class="form-control" value="<?php echo htmlspecialchars($username ?? ''); ?>" required>
                                            <small class="form-hint">Must be unique and at least 3 characters long.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Email Address</label>
                                            <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">First Name</label>
                                            <input type="text" name="first_name" class="form-control" value="<?php echo htmlspecialchars($first_name ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Last Name</label>
                                            <input type="text" name="last_name" class="form-control" value="<?php echo htmlspecialchars($last_name ?? ''); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" name="phone" class="form-control" value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date of Birth</label>
                                            <input type="date" name="date_of_birth" class="form-control" value="<?php echo htmlspecialchars($date_of_birth ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Address</label>
                                    <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($address ?? ''); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Account Type</label>
                                            <select name="account_type" class="form-select">
                                                <option value="savings" <?php echo ($account_type ?? 'savings') === 'savings' ? 'selected' : ''; ?>>Savings</option>
                                                <option value="checking" <?php echo ($account_type ?? '') === 'checking' ? 'selected' : ''; ?>>Checking</option>
                                                <option value="business" <?php echo ($account_type ?? '') === 'business' ? 'selected' : ''; ?>>Business</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Initial Balance</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" name="initial_balance" class="form-control" step="0.01" min="0" value="<?php echo htmlspecialchars($initial_balance ?? '0'); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Account Status</label>
                                            <select name="status" class="form-select">
                                                <option value="active" <?php echo ($status ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                                                <option value="suspended" <?php echo ($status ?? '') === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">KYC Status</label>
                                            <select name="kyc_status" class="form-select">
                                                <option value="pending" <?php echo ($kyc_status ?? 'pending') === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                <option value="verified" <?php echo ($kyc_status ?? '') === 'verified' ? 'selected' : ''; ?>>Verified</option>
                                                <option value="rejected" <?php echo ($kyc_status ?? '') === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Password</label>
                                            <input type="password" name="password" class="form-control" required>
                                            <small class="form-hint">Minimum 6 characters.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Confirm Password</label>
                                            <input type="password" name="confirm_password" class="form-control" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-footer">
                                    <div class="btn-list">
                                        <a href="users.php" class="btn">
                                            Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary" data-original-text="Create User">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <line x1="12" y1="5" x2="12" y2="19"/>
                                                <line x1="5" y1="12" x2="19" y2="12"/>
                                            </svg>
                                            Create User
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
