        <?php if (isLoggedIn()): ?>
        </div> <!-- End page-wrapper -->
        <?php endif; ?>
    </div> <!-- End page -->
    
    <!-- Security indicator for session timeout -->
    <?php if (isLoggedIn()): ?>
    <div class="security-indicator">
        <div class="alert alert-info alert-dismissible d-none" id="session-warning" role="alert">
            <div class="d-flex">
                <div>
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <circle cx="12" cy="12" r="9"/>
                        <polyline points="12 7 12 12 15 15"/>
                    </svg>
                </div>
                <div>
                    <h4 class="alert-title">Session Expiring Soon</h4>
                    <div class="text-muted">Your session will expire in <span id="session-countdown">5</span> minutes. Click to extend.</div>
                </div>
            </div>
            <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Flash Messages -->
    <?php if (hasFlashMessage('success')): ?>
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M5 12l5 5l10 -10"/>
                </svg>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                <?php echo getFlashMessage('success'); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('error')): ?>
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-danger text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <circle cx="12" cy="12" r="9"/>
                    <line x1="15" y1="9" x2="9" y2="15"/>
                    <line x1="9" y1="9" x2="15" y2="15"/>
                </svg>
                <strong class="me-auto">Error</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                <?php echo getFlashMessage('error'); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('warning')): ?>
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-warning text-white">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <path d="M12 9v2m0 4v.01"/>
                    <path d="M5 19h14a2 2 0 0 0 1.84 -2.75l-7.1 -12.25a2 2 0 0 0 -3.5 0l-7.1 12.25a2 2 0 0 0 1.75 2.75"/>
                </svg>
                <strong class="me-auto">Warning</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                <?php echo getFlashMessage('warning'); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Custom JavaScript -->
    <script>
        // Session timeout warning
        <?php if (isLoggedIn()): ?>
        let sessionTimeout = <?php echo SESSION_TIMEOUT; ?> * 1000; // Convert to milliseconds
        let warningTime = 5 * 60 * 1000; // 5 minutes before expiry
        let countdownInterval;
        
        setTimeout(function() {
            document.getElementById('session-warning').classList.remove('d-none');
            let countdown = 5 * 60; // 5 minutes in seconds
            
            countdownInterval = setInterval(function() {
                countdown--;
                let minutes = Math.floor(countdown / 60);
                let seconds = countdown % 60;
                document.getElementById('session-countdown').textContent = 
                    minutes + ':' + (seconds < 10 ? '0' : '') + seconds;
                
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    window.location.href = '/auth/logout.php';
                }
            }, 1000);
        }, sessionTimeout - warningTime);
        
        // Extend session on user activity
        document.addEventListener('click', function() {
            fetch('/auth/extend-session.php', {method: 'POST'});
        });
        <?php endif; ?>
        
        // Auto-hide toasts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const toasts = document.querySelectorAll('.toast');
            toasts.forEach(function(toast) {
                setTimeout(function() {
                    const bsToast = new bootstrap.Toast(toast);
                    bsToast.hide();
                }, 5000);
            });
        });
        
        // Form validation helpers
        function validateAmount(input) {
            const value = parseFloat(input.value);
            const min = <?php echo MIN_TRANSFER_AMOUNT; ?>;
            const max = <?php echo MAX_TRANSFER_AMOUNT; ?>;
            
            if (isNaN(value) || value < min || value > max) {
                input.setCustomValidity(`Amount must be between ${min} and ${max}`);
                return false;
            } else {
                input.setCustomValidity('');
                return true;
            }
        }
        
        // Format currency inputs
        function formatCurrency(input) {
            let value = input.value.replace(/[^\d.]/g, '');
            let parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }
            if (parts[1] && parts[1].length > 2) {
                value = parts[0] + '.' + parts[1].substring(0, 2);
            }
            input.value = value;
        }
        
        // Confirm dangerous actions
        function confirmAction(message) {
            return confirm(message || 'Are you sure you want to perform this action?');
        }
        
        // Loading state for forms
        function setFormLoading(form, loading = true) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const inputs = form.querySelectorAll('input, select, textarea');
            
            if (loading) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Processing...';
                inputs.forEach(input => input.disabled = true);
            } else {
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'Submit';
                inputs.forEach(input => input.disabled = false);
            }
        }
    </script>
    
    <footer class="footer footer-transparent d-print-none">
        <div class="container-xl">
            <div class="row text-center align-items-center flex-row-reverse">
                <div class="col-lg-auto ms-lg-auto">
                    <ul class="list-inline list-inline-dots mb-0">
                        <li class="list-inline-item">
                            <a href="/support/" class="link-secondary">Support</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="#" class="link-secondary">Privacy Policy</a>
                        </li>
                        <li class="list-inline-item">
                            <a href="#" class="link-secondary">Terms of Service</a>
                        </li>
                    </ul>
                </div>
                <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                    <ul class="list-inline list-inline-dots mb-0">
                        <li class="list-inline-item">
                            Copyright &copy; <?php echo date('Y'); ?>
                            <a href="/" class="link-secondary"><?php echo APP_NAME; ?></a>.
                            All rights reserved.
                        </li>
                        <li class="list-inline-item">
                            <a href="#" class="link-secondary" rel="noopener">
                                v<?php echo APP_VERSION; ?>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
