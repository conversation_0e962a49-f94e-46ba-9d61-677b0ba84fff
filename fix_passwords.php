<?php
/**
 * Fix Password Hashes Script
 * This script updates the password hashes in the database to ensure they work correctly
 */

require_once 'config/config.php';

echo "<h2>Password Hash Fix Script</h2>";
echo "<hr>";

try {
    $db = getDB();
    
    // Generate correct password hashes
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $user_password = password_hash('user123', PASSWORD_DEFAULT);
    
    echo "<h3>Step 1: Generating New Password Hashes</h3>";
    echo "✅ <strong>Admin password hash:</strong> " . substr($admin_password, 0, 30) . "...<br>";
    echo "✅ <strong>User password hash:</strong> " . substr($user_password, 0, 30) . "...<br><br>";
    
    echo "<h3>Step 2: Updating Admin Password</h3>";
    $sql = "UPDATE accounts SET password = ? WHERE username = 'admin'";
    $result = $db->query($sql, [$admin_password]);
    echo "✅ <strong>SUCCESS:</strong> Admin password updated<br><br>";
    
    echo "<h3>Step 3: Updating User Password</h3>";
    $sql = "UPDATE accounts SET password = ? WHERE username = 'john_doe'";
    $result = $db->query($sql, [$user_password]);
    echo "✅ <strong>SUCCESS:</strong> User password updated<br><br>";
    
    echo "<h3>Step 4: Verifying Password Updates</h3>";
    
    // Test admin password
    $sql = "SELECT password FROM accounts WHERE username = 'admin'";
    $result = $db->query($sql);
    $admin_hash = $result->fetch_assoc()['password'];
    
    if (password_verify('admin123', $admin_hash)) {
        echo "✅ <strong>Admin password verification:</strong> PASSED<br>";
    } else {
        echo "❌ <strong>Admin password verification:</strong> FAILED<br>";
    }
    
    // Test user password
    $sql = "SELECT password FROM accounts WHERE username = 'john_doe'";
    $result = $db->query($sql);
    $user_hash = $result->fetch_assoc()['password'];
    
    if (password_verify('user123', $user_hash)) {
        echo "✅ <strong>User password verification:</strong> PASSED<br>";
    } else {
        echo "❌ <strong>User password verification:</strong> FAILED<br>";
    }
    
    echo "<br>";
    echo "<h3>🎯 Summary</h3>";
    echo "🎉 <strong>SUCCESS!</strong> Password hashes have been updated and verified!<br>";
    echo "🚀 <strong>You can now login with:</strong><br>";
    echo "   • <strong>Admin:</strong> admin / admin123<br>";
    echo "   • <strong>User:</strong> john_doe / user123<br><br>";
    
    echo "🔗 <strong>Test Login:</strong> <a href='auth/login.php' target='_blank'>Go to Login Page</a><br>";
    
} catch (Exception $e) {
    echo "❌ <strong>ERROR:</strong> " . $e->getMessage() . "<br>";
}

echo "<br><hr>";
echo "<p><small>💡 <strong>Tip:</strong> Delete this file (fix_passwords.php) after confirming login works.</small></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
h2, h3 { color: #333; }
hr { border: 1px solid #ddd; }
</style>
