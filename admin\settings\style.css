/* Admin Settings Specific Styles */

/* Settings Cards */
.settings-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(0,0,0,0.1);
    border-color: #cbd5e1;
}

.settings-card .card-header {
    background: linear-gradient(135deg, #f1f5f9 0%, #ffffff 100%);
    border-bottom: 2px solid #e2e8f0;
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
}

.settings-card .card-title {
    font-weight: 600;
    color: #1e293b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.settings-card .card-title .icon {
    color: #3b82f6;
}

/* Form Elements */
.settings-form .form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.settings-form .form-control,
.settings-form .form-select {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.settings-form .form-control:focus,
.settings-form .form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    outline: none;
}

/* Theme Selection */
.form-selectgroup {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
}

.form-selectgroup-item {
    flex: 1;
}

.form-selectgroup-input {
    display: none;
}

.form-selectgroup-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #374151;
}

.form-selectgroup-label:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.form-selectgroup-input:checked + .form-selectgroup-label {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.form-selectgroup-label .icon {
    width: 20px;
    height: 20px;
}

/* Switch Controls */
.form-check.form-switch {
    padding-left: 3rem;
    margin-bottom: 1rem;
}

.form-check.form-switch .form-check-input {
    width: 2.5rem;
    height: 1.25rem;
    border-radius: 2rem;
    background-color: #e5e7eb;
    border: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.form-check.form-switch .form-check-input:checked {
    background-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.form-check.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-check-label {
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    margin-left: 0.5rem;
}

/* Color Scheme Preview */
.color-scheme-preview {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.color-dot {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.color-dot:hover {
    transform: scale(1.1);
}

.color-dot.blue { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.color-dot.green { background: linear-gradient(135deg, #10b981, #059669); }
.color-dot.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.color-dot.red { background: linear-gradient(135deg, #ef4444, #dc2626); }
.color-dot.orange { background: linear-gradient(135deg, #f59e0b, #d97706); }

/* Input Groups */
.input-group {
    border-radius: 12px;
    overflow: hidden;
}

.input-group-text {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border: 2px solid #e5e7eb;
    border-right: none;
    color: #64748b;
    font-weight: 600;
}

.input-group .form-control {
    border-left: none;
}

.input-group:focus-within .input-group-text {
    border-color: #3b82f6;
}

/* Form Footer */
.form-footer {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-top: 2px solid #e2e8f0;
    padding: 1.5rem;
    border-radius: 0 0 16px 16px;
    margin-top: 1.5rem;
}

.form-footer .btn {
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.form-footer .btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: none;
    color: white;
}

.form-footer .btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Form Hints */
.form-hint {
    font-size: 0.75rem;
    color: #64748b;
    margin-top: 0.25rem;
    font-style: italic;
}

/* Alert Styles */
.alert {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
}

.alert-success {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border-left: 4px solid #10b981;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border-left: 4px solid #ef4444;
}

.alert .alert-icon {
    margin-right: 0.5rem;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Settings Preview */
.settings-preview {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border: 2px dashed #cbd5e1;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    margin-top: 1rem;
}

.settings-preview h4 {
    color: #475569;
    margin-bottom: 1rem;
}

.settings-preview p {
    color: #64748b;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-card {
        margin-bottom: 1.5rem;
    }
    
    .form-selectgroup {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .form-selectgroup-label {
        padding: 0.75rem;
    }
    
    .color-scheme-preview {
        padding: 0.75rem;
    }
    
    .form-footer {
        padding: 1rem;
    }
    
    .form-footer .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .settings-card {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: #374151;
    }
    
    .settings-card .card-header {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border-bottom-color: #4b5563;
    }
    
    .settings-card .card-title {
        color: #e5e7eb;
    }
    
    .settings-form .form-label {
        color: #e5e7eb;
    }
    
    .settings-form .form-control,
    .settings-form .form-select {
        background: #374151;
        border-color: #4b5563;
        color: #e5e7eb;
    }
    
    .settings-form .form-control:focus,
    .settings-form .form-select:focus {
        border-color: #3b82f6;
        background: #374151;
    }
    
    .form-selectgroup-label {
        background: #374151;
        border-color: #4b5563;
        color: #e5e7eb;
    }
    
    .form-selectgroup-label:hover {
        background: #4b5563;
    }
    
    .form-check-label {
        color: #e5e7eb;
    }
    
    .color-scheme-preview {
        background: #374151;
        border-color: #4b5563;
    }
    
    .input-group-text {
        background: linear-gradient(135deg, #4b5563, #374151);
        border-color: #4b5563;
        color: #9ca3af;
    }
    
    .form-footer {
        background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
        border-top-color: #4b5563;
    }
    
    .settings-preview {
        background: linear-gradient(135deg, #374151, #4b5563);
        border-color: #6b7280;
    }
    
    .settings-preview h4 {
        color: #e5e7eb;
    }
    
    .settings-preview p {
        color: #9ca3af;
    }
}
