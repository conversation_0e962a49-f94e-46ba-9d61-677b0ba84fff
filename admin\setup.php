<?php
require_once '../config/config.php';

// This script creates the default admin user
// Run this once to set up the admin account

try {
    $db = getDB();
    
    // Check if admin user already exists
    $check_admin = $db->query("SELECT id FROM accounts WHERE username = 'admin' AND is_admin = 1");
    
    if ($check_admin->num_rows > 0) {
        echo "Admin user already exists!<br>";
        echo "<a href='login.php'>Go to Admin Login</a>";
        exit();
    }
    
    // Create admin user
    $admin_data = [
        'account_number' => '**********',
        'username' => 'admin',
        'password' => hashPassword('admin123'),
        'email' => '<EMAIL>',
        'first_name' => 'System',
        'last_name' => 'Administrator',
        'phone' => '******-0000',
        'address' => 'System Administration',
        'account_type' => 'admin',
        'balance' => 0,
        'status' => 'active',
        'kyc_status' => 'verified',
        'is_admin' => 1
    ];
    
    $sql = "INSERT INTO accounts (
                account_number, username, password, email, first_name, last_name,
                phone, address, account_type, balance, status, kyc_status, is_admin
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $admin_data['account_number'],
        $admin_data['username'],
        $admin_data['password'],
        $admin_data['email'],
        $admin_data['first_name'],
        $admin_data['last_name'],
        $admin_data['phone'],
        $admin_data['address'],
        $admin_data['account_type'],
        $admin_data['balance'],
        $admin_data['status'],
        $admin_data['kyc_status'],
        $admin_data['is_admin']
    ];
    
    $admin_id = $db->insert($sql, $params);
    
    if ($admin_id) {
        echo "<h2>✅ Admin User Created Successfully!</h2>";
        echo "<div style='background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 20px 0; font-family: Arial, sans-serif;'>";
        echo "<h3>Admin Login Credentials:</h3>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
        echo "<p><strong>Account Number:</strong> **********</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "</div>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='login.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px; display: inline-block;'>Go to Admin Login</a>";
        echo "</div>";
        echo "<div style='background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px; margin: 20px 0;'>";
        echo "<p><strong>⚠️ Security Note:</strong> Please change the default password after first login!</p>";
        echo "</div>";
    } else {
        echo "<h2>❌ Failed to create admin user</h2>";
        echo "<p>Please check the database connection and try again.</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error creating admin user</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    error_log("Admin setup error: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>Admin Setup - SecureBank Online</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f8fafc;
        }
        
        h2 {
            color: #1e293b;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .success {
            color: #059669;
        }
        
        .error {
            color: #dc2626;
        }
        
        a {
            color: #3b82f6;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SecureBank Online - Admin Setup</h1>
        <p>This script sets up the default administrator account for the banking system.</p>
        
        <?php if (!isset($admin_id)): ?>
        <div style="background: #fee2e2; border: 1px solid #ef4444; border-radius: 8px; padding: 15px; margin: 20px 0;">
            <p><strong>Setup Required:</strong> No admin user found. Please run this script to create the default admin account.</p>
        </div>
        <?php endif; ?>
        
        <hr style="margin: 30px 0;">
        
        <h3>After Setup:</h3>
        <ol>
            <li>Use the admin credentials to log into the admin panel</li>
            <li>Change the default password in the admin settings</li>
            <li>Configure system settings as needed</li>
            <li>Delete or secure this setup file</li>
        </ol>
        
        <div style="margin-top: 30px;">
            <a href="../">← Back to Main Site</a> | 
            <a href="login.php">Admin Login</a>
        </div>
    </div>
</body>
</html>
