<?php
require_once '../config/config.php';
requireAdmin();

$user_id = intval($_GET['id'] ?? 0);

if ($user_id <= 0) {
    setFlashMessage('error', 'Invalid user ID.');
    redirect('users.php');
}

try {
    $db = getDB();
    
    // Get user info first
    $user_result = $db->query("SELECT username, first_name, last_name, status FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
    
    if ($user_result->num_rows === 0) {
        setFlashMessage('error', 'User not found.');
        redirect('users.php');
    }
    
    $user = $user_result->fetch_assoc();
    
    if ($user['status'] === 'suspended') {
        setFlashMessage('warning', 'User is already suspended.');
        redirect('users.php');
    }
    
    // Suspend the user
    $db->query("UPDATE accounts SET status = 'suspended' WHERE id = ?", [$user_id]);
    
    // Log the activity
    logActivity($_SESSION['user_id'], 'Admin suspended user', 'accounts', $user_id, 
                ['status' => $user['status']], ['status' => 'suspended']);
    
    setFlashMessage('success', "User {$user['first_name']} {$user['last_name']} has been suspended.");
    
} catch (Exception $e) {
    error_log("Suspend user error: " . $e->getMessage());
    setFlashMessage('error', 'Failed to suspend user. Please try again.');
}

redirect('users.php');
?>
