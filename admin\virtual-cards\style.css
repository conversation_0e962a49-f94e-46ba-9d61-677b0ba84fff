/* Virtual Cards Management Styles */

/* Card Type Selection */
.form-selectgroup {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.form-selectgroup-item {
    flex: 1;
}

.form-selectgroup-input {
    display: none;
}

.form-selectgroup-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-selectgroup-label:hover {
    border-color: #3b82f6;
    background: #f8fafc;
}

.form-selectgroup-input:checked + .form-selectgroup-label {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
}

/* Card Type Logos */
.card-type-logo {
    font-weight: 700;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    color: white;
    text-align: center;
    min-width: 40px;
}

.card-type-logo.visa {
    background: linear-gradient(135deg, #1a1f71, #0f3460);
}

.card-type-logo.mastercard {
    background: linear-gradient(135deg, #eb001b, #f79e1b);
}

.card-type-logo.amex {
    background: linear-gradient(135deg, #006fcf, #0048a3);
}

/* Virtual Card Display */
.virtual-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    min-height: 280px;
    display: flex;
    flex-direction: column;
}

.virtual-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

/* Card Type Specific Gradients */
.virtual-card.visa {
    background: linear-gradient(135deg, #1a1f71 0%, #0f3460 100%);
}

.virtual-card.mastercard {
    background: linear-gradient(135deg, #eb001b 0%, #f79e1b 100%);
}

.virtual-card.amex {
    background: linear-gradient(135deg, #006fcf 0%, #0048a3 100%);
}

/* Card Status */
.virtual-card.blocked {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    opacity: 0.8;
}

.virtual-card.expired {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    opacity: 0.8;
}

/* Card Header Info */
.card-header-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-type-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.card-status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-status-badge.status-active {
    background: rgba(34, 197, 94, 0.2);
    color: #bbf7d0;
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.card-status-badge.status-blocked {
    background: rgba(239, 68, 68, 0.2);
    color: #fecaca;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.card-status-badge.status-expired {
    background: rgba(156, 163, 175, 0.2);
    color: #d1d5db;
    border: 1px solid rgba(156, 163, 175, 0.3);
}

/* Card Number */
.card-number {
    font-family: 'Courier New', monospace;
    font-size: 1.25rem;
    font-weight: 600;
    letter-spacing: 2px;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Card Details */
.card-details {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.card-details .label {
    font-size: 0.6rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.card-details .value {
    font-size: 0.875rem;
    font-weight: 600;
}

/* Card Balance */
.card-balance {
    margin-top: auto;
    margin-bottom: 1rem;
}

.balance-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.balance-info .label {
    font-size: 0.75rem;
    opacity: 0.8;
}

.balance-info .amount {
    font-weight: 600;
    font-size: 0.875rem;
}

.progress {
    height: 4px;
    background: rgba(255,255,255,0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, #34d399, #10b981);
    transition: width 0.3s ease;
}

/* Card Actions */
.card-actions {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.card-actions .btn {
    flex: 1;
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
    border-radius: 6px;
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.card-actions .btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
    transform: translateY(-1px);
}

/* Card User Info */
.card-user-info {
    margin-top: auto;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.card-user-info small {
    color: rgba(255,255,255,0.8) !important;
    font-size: 0.7rem;
}

/* Create Card Form */
.create-card-form {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

/* Card Preview */
.card-preview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 1rem;
    color: white;
    margin-top: 1rem;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.card-preview.visa {
    background: linear-gradient(135deg, #1a1f71 0%, #0f3460 100%);
}

.card-preview.mastercard {
    background: linear-gradient(135deg, #eb001b 0%, #f79e1b 100%);
}

.card-preview.amex {
    background: linear-gradient(135deg, #006fcf 0%, #0048a3 100%);
}

.card-preview-number {
    font-family: 'Courier New', monospace;
    font-size: 1rem;
    letter-spacing: 1px;
    margin: 1rem 0;
}

.card-preview-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .virtual-card {
        margin-bottom: 1rem;
    }
    
    .card-details {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .card-actions .btn {
        flex: none;
    }
    
    .balance-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .form-selectgroup {
        flex-direction: column;
        gap: 0.75rem;
    }
}

/* Loading States */
.card-loading {
    opacity: 0.6;
    pointer-events: none;
}

.card-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Animations */
.card-success {
    animation: successPulse 0.6s ease-out;
}

.card-error {
    animation: errorShake 0.6s ease-out;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Card Chip Effect */
.virtual-card::before {
    content: '';
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 30px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    border-radius: 4px;
    opacity: 0.8;
}

/* Holographic Effect */
.virtual-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.virtual-card:hover::after {
    opacity: 1;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .create-card-form {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: #374151;
    }
    
    .form-selectgroup-label {
        background: #374151;
        border-color: #4b5563;
        color: #e5e7eb;
    }
    
    .form-selectgroup-label:hover {
        border-color: #3b82f6;
        background: #4b5563;
    }
    
    .form-selectgroup-input:checked + .form-selectgroup-label {
        background: linear-gradient(135deg, #1e3a8a, #1e40af);
        border-color: #3b82f6;
        color: white;
    }
}
