<?php
/**
 * Main Configuration File for Online Banking System
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Application configuration
define('APP_NAME', 'SecureBank Online');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/online_banking');
define('ADMIN_EMAIL', '<EMAIL>');

// Security configuration
define('SESSION_TIMEOUT', 1800); // 30 minutes
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// File upload configuration
define('UPLOAD_MAX_SIZE', 5242880); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);
define('UPLOAD_PATH', __DIR__ . '/../assets/uploads/');

// Currency and transfer limits
define('DEFAULT_CURRENCY', 'USD');
define('MIN_TRANSFER_AMOUNT', 1.00);
define('MAX_TRANSFER_AMOUNT', 10000.00);
define('DAILY_TRANSFER_LIMIT', 25000.00);

// Include database configuration
require_once __DIR__ . '/database.php';

/**
 * Security Functions
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function generateAccountNumber() {
    $prefix = '10';
    $timestamp = time();
    $random = mt_rand(1000, 9999);
    return $prefix . substr($timestamp, -6) . $random;
}

function generateTransactionId() {
    return 'TXN' . date('Ymd') . mt_rand(100000, 999999);
}

function generateTicketNumber() {
    return 'TKT' . date('Ymd') . mt_rand(1000, 9999);
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['username']);
}

function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: /auth/login.php');
        exit();
    }
}

function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: /dashboard/');
        exit();
    }
}

function checkSessionTimeout() {
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            session_destroy();
            header('Location: /auth/login.php?timeout=1');
            exit();
        }
    }
    $_SESSION['last_activity'] = time();
}

function logActivity($user_id, $action, $table_name = null, $record_id = null, $old_values = null, $new_values = null) {
    try {
        $db = getDB();
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $sql = "INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $user_id,
            $action,
            $table_name,
            $record_id,
            $old_values ? json_encode($old_values) : null,
            $new_values ? json_encode($new_values) : null,
            $ip_address,
            $user_agent
        ];
        
        $db->query($sql, $params);
    } catch (Exception $e) {
        error_log("Failed to log activity: " . $e->getMessage());
    }
}

function recordLoginAttempt($username, $success = false) {
    try {
        $db = getDB();
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        
        $sql = "INSERT INTO login_attempts (username, ip_address, success) VALUES (?, ?, ?)";
        $db->query($sql, [$username, $ip_address, $success ? 1 : 0]);
    } catch (Exception $e) {
        error_log("Failed to record login attempt: " . $e->getMessage());
    }
}

function getFailedLoginAttempts($username, $ip_address) {
    try {
        $db = getDB();
        $since = date('Y-m-d H:i:s', time() - LOGIN_LOCKOUT_TIME);
        
        $sql = "SELECT COUNT(*) as attempts FROM login_attempts 
                WHERE (username = ? OR ip_address = ?) 
                AND success = 0 AND attempted_at > ?";
        
        $result = $db->query($sql, [$username, $ip_address, $since]);
        $row = $result->fetch_assoc();
        
        return $row['attempts'] ?? 0;
    } catch (Exception $e) {
        error_log("Failed to get login attempts: " . $e->getMessage());
        return 0;
    }
}

function formatCurrency($amount, $currency = DEFAULT_CURRENCY) {
    $amount = $amount ?? 0; // Handle null values
    return $currency . ' ' . number_format($amount, 2);
}

function formatDate($date, $format = 'M d, Y H:i') {
    return date($format, strtotime($date));
}

function redirect($url) {
    // Handle relative URLs
    if (!str_starts_with($url, 'http') && !str_starts_with($url, '/')) {
        $url = '/' . ltrim($url, '/');
    }
    header("Location: $url");
    exit();
}

function setFlashMessage($type, $message) {
    $_SESSION['flash'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash'][$type])) {
        $message = $_SESSION['flash'][$type];
        unset($_SESSION['flash'][$type]);
        return $message;
    }
    return null;
}

function hasFlashMessage($type) {
    return isset($_SESSION['flash'][$type]);
}

// Initialize session timeout check for logged-in users
if (isLoggedIn()) {
    checkSessionTimeout();
}
?>
