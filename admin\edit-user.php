<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Edit User';

$errors = [];
$success = '';
$user_id = intval($_GET['id'] ?? 0);

if ($user_id <= 0) {
    redirect('users.php');
}

// Get user data
try {
    $db = getDB();
    $sql = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
    $result = $db->query($sql, [$user_id]);
    
    if ($result->num_rows === 0) {
        setFlashMessage('error', 'User not found.');
        redirect('users.php');
    }
    
    $user = $result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Edit user fetch error: " . $e->getMessage());
    setFlashMessage('error', 'Error loading user data.');
    redirect('users.php');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate input
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $first_name = sanitizeInput($_POST['first_name'] ?? '');
    $last_name = sanitizeInput($_POST['last_name'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $address = sanitizeInput($_POST['address'] ?? '');
    $date_of_birth = sanitizeInput($_POST['date_of_birth'] ?? '');
    $account_type = sanitizeInput($_POST['account_type'] ?? 'savings');
    $balance = floatval($_POST['balance'] ?? 0);
    $kyc_status = sanitizeInput($_POST['kyc_status'] ?? 'pending');
    $status = sanitizeInput($_POST['status'] ?? 'active');
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // Validation
    if (empty($username)) {
        $errors[] = 'Username is required.';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Username must be at least 3 characters long.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (empty($first_name)) {
        $errors[] = 'First name is required.';
    }
    
    if (empty($last_name)) {
        $errors[] = 'Last name is required.';
    }
    
    if (!empty($new_password)) {
        if (strlen($new_password) < 6) {
            $errors[] = 'Password must be at least 6 characters long.';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'Passwords do not match.';
        }
    }
    
    if (!empty($date_of_birth)) {
        $dob = DateTime::createFromFormat('Y-m-d', $date_of_birth);
        if (!$dob || $dob->format('Y-m-d') !== $date_of_birth) {
            $errors[] = 'Please enter a valid date of birth.';
        }
    }
    
    if ($balance < 0) {
        $errors[] = 'Balance cannot be negative.';
    }
    
    // Check for existing username and email (excluding current user)
    if (empty($errors)) {
        try {
            // Check username
            $check_username = $db->query("SELECT id FROM accounts WHERE username = ? AND id != ?", [$username, $user_id]);
            if ($check_username->num_rows > 0) {
                $errors[] = 'Username already exists.';
            }
            
            // Check email
            $check_email = $db->query("SELECT id FROM accounts WHERE email = ? AND id != ?", [$email, $user_id]);
            if ($check_email->num_rows > 0) {
                $errors[] = 'Email address already exists.';
            }
            
        } catch (Exception $e) {
            $errors[] = 'Database error occurred. Please try again.';
            error_log("Edit user validation error: " . $e->getMessage());
        }
    }
    
    // Update user if no errors
    if (empty($errors)) {
        try {
            $db->beginTransaction();
            
            // Prepare update data
            $update_fields = [
                'username' => $username,
                'email' => $email,
                'first_name' => $first_name,
                'last_name' => $last_name,
                'phone' => $phone,
                'address' => $address,
                'date_of_birth' => $date_of_birth ?: null,
                'account_type' => $account_type,
                'balance' => $balance,
                'kyc_status' => $kyc_status,
                'status' => $status
            ];
            
            // Add password if provided
            if (!empty($new_password)) {
                $update_fields['password'] = hashPassword($new_password);
            }
            
            // Build SQL
            $set_clauses = [];
            $params = [];
            foreach ($update_fields as $field => $value) {
                $set_clauses[] = "$field = ?";
                $params[] = $value;
            }
            $params[] = $user_id;
            
            $sql = "UPDATE accounts SET " . implode(', ', $set_clauses) . " WHERE id = ?";
            $db->query($sql, $params);
            
            // Log the activity
            logActivity($_SESSION['user_id'], 'Admin updated user', 'accounts', $user_id, $user, $update_fields);
            
            $db->commit();
            
            $success = "User updated successfully!";
            
            // Refresh user data
            $result = $db->query("SELECT * FROM accounts WHERE id = ?", [$user_id]);
            $user = $result->fetch_assoc();
            
        } catch (Exception $e) {
            $db->rollback();
            $errors[] = 'Failed to update user. Please try again.';
            error_log("Edit user error: " . $e->getMessage());
        }
    }
}

include '../includes/header.php';
?>

<?php include '../includes/sidebar.php'; ?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
                            <li class="breadcrumb-item"><a href="users.php">Users</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Edit User</li>
                        </ol>
                    </nav>
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Edit User: <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                    </h2>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="view-user.php?id=<?php echo $user_id; ?>" class="btn btn-outline-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <circle cx="12" cy="12" r="2"/>
                                <path d="M22 12c-2.667 3.333 -6 5 -10 5s-7.333 -1.667 -10 -5c2.667 -3.333 6 -5 10 -5s7.333 1.667 10 5"/>
                            </svg>
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">User Information</h3>
                            <div class="card-actions">
                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <line x1="15" y1="9" x2="9" y2="15"/>
                                            <line x1="9" y1="9" x2="15" y2="15"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="alert-title">Error!</h4>
                                        <ul class="mb-0">
                                            <?php foreach ($errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($success)): ?>
                            <div class="alert alert-success" role="alert">
                                <div class="d-flex">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M5 12l5 5l10 -10"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="alert-title">Success!</h4>
                                        <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Account Info -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <div class="font-weight-medium">Account Number</div>
                                                    <div class="text-muted"><?php echo htmlspecialchars($user['account_number']); ?></div>
                                                </div>
                                                <div class="ms-auto">
                                                    <span class="avatar avatar-sm"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <div class="font-weight-medium">Member Since</div>
                                                    <div class="text-muted"><?php echo formatDate($user['created_at'], 'M d, Y'); ?></div>
                                                </div>
                                                <div class="ms-auto">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon text-muted" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
                                                        <line x1="16" y1="2" x2="16" y2="6"/>
                                                        <line x1="8" y1="2" x2="8" y2="6"/>
                                                        <line x1="3" y1="10" x2="21" y2="10"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <form method="POST" action="">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Username</label>
                                            <input type="text" name="username" class="form-control" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Email Address</label>
                                            <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">First Name</label>
                                            <input type="text" name="first_name" class="form-control" value="<?php echo htmlspecialchars($user['first_name']); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Last Name</label>
                                            <input type="text" name="last_name" class="form-control" value="<?php echo htmlspecialchars($user['last_name']); ?>" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Phone Number</label>
                                            <input type="tel" name="phone" class="form-control" value="<?php echo htmlspecialchars($user['phone']); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Date of Birth</label>
                                            <input type="date" name="date_of_birth" class="form-control" value="<?php echo htmlspecialchars($user['date_of_birth']); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Address</label>
                                    <textarea name="address" class="form-control" rows="3"><?php echo htmlspecialchars($user['address']); ?></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Account Type</label>
                                            <select name="account_type" class="form-select">
                                                <option value="savings" <?php echo $user['account_type'] === 'savings' ? 'selected' : ''; ?>>Savings</option>
                                                <option value="checking" <?php echo $user['account_type'] === 'checking' ? 'selected' : ''; ?>>Checking</option>
                                                <option value="business" <?php echo $user['account_type'] === 'business' ? 'selected' : ''; ?>>Business</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Balance</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" name="balance" class="form-control" step="0.01" min="0" value="<?php echo htmlspecialchars($user['balance']); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Account Status</label>
                                            <select name="status" class="form-select">
                                                <option value="active" <?php echo $user['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                                <option value="suspended" <?php echo $user['status'] === 'suspended' ? 'selected' : ''; ?>>Suspended</option>
                                                <option value="closed" <?php echo $user['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">KYC Status</label>
                                            <select name="kyc_status" class="form-select">
                                                <option value="pending" <?php echo $user['kyc_status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                <option value="verified" <?php echo $user['kyc_status'] === 'verified' ? 'selected' : ''; ?>>Verified</option>
                                                <option value="rejected" <?php echo $user['kyc_status'] === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <hr>
                                <h4>Change Password (Optional)</h4>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">New Password</label>
                                            <input type="password" name="new_password" class="form-control">
                                            <small class="form-hint">Leave blank to keep current password.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Confirm New Password</label>
                                            <input type="password" name="confirm_password" class="form-control">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-footer">
                                    <div class="btn-list">
                                        <a href="users.php" class="btn">
                                            Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary" data-original-text="Update User">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                                                <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                                                <path d="M16 5l3 3"/>
                                            </svg>
                                            Update User
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
