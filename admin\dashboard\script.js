/**
 * Admin Dashboard JavaScript
 * Handles dashboard-specific functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    initializeStatCards();
    initializeQuickActions();
    initializeRealTimeUpdates();
});

/**
 * Initialize Dashboard
 */
function initializeDashboard() {
    // Add loading animation to stat cards
    const statCards = document.querySelectorAll('.admin-stat-card');
    statCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });

    // Add hover effects to quick actions
    const quickActions = document.querySelectorAll('.quick-action-item');
    quickActions.forEach(action => {
        action.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });
        
        action.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Initialize tooltips for stat cards
    initializeStatTooltips();
}

/**
 * Initialize Stat Cards
 */
function initializeStatCards() {
    const statCards = document.querySelectorAll('.admin-stat-card');
    
    statCards.forEach(card => {
        // Add click animation
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });

        // Add number animation on load
        const numberElement = card.querySelector('.h1[data-stat]');
        if (numberElement) {
            animateNumber(numberElement);
        }
    });
}

/**
 * Initialize Quick Actions
 */
function initializeQuickActions() {
    const quickActions = document.querySelectorAll('.quick-action-item');
    
    quickActions.forEach((action, index) => {
        // Stagger animation
        action.style.animationDelay = `${0.5 + (index * 0.1)}s`;
        action.classList.add('fade-in-up');
        
        // Add ripple effect on click
        action.addEventListener('click', function(e) {
            createRippleEffect(e, this);
        });
    });
}

/**
 * Initialize Real-time Updates
 */
function initializeRealTimeUpdates() {
    // Update stats every 30 seconds
    setInterval(updateDashboardStats, 30000);
    
    // Update recent activity every 60 seconds
    setInterval(updateRecentActivity, 60000);
    
    // Show last updated time
    updateLastRefreshTime();
    setInterval(updateLastRefreshTime, 1000);
}

/**
 * Animate Numbers
 */
function animateNumber(element) {
    const finalValue = parseInt(element.textContent.replace(/,/g, ''));
    const duration = 2000;
    const startTime = Date.now();
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // Easing function
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(finalValue * easeOutQuart);
        
        element.textContent = currentValue.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    element.textContent = '0';
    requestAnimationFrame(updateNumber);
}

/**
 * Create Ripple Effect
 */
function createRippleEffect(event, element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 1;
    `;
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
        ripple.remove();
    }, 600);
}

/**
 * Update Dashboard Stats
 */
function updateDashboardStats() {
    fetch('../../api/admin-stats.php')
        .then(response => response.json())
        .then(data => {
            Object.keys(data).forEach(key => {
                const element = document.querySelector(`[data-stat="${key}"]`);
                if (element) {
                    const oldValue = parseInt(element.textContent.replace(/,/g, ''));
                    const newValue = parseInt(data[key]);
                    
                    if (oldValue !== newValue) {
                        // Highlight change
                        element.parentElement.classList.add('stat-updated');
                        setTimeout(() => {
                            element.parentElement.classList.remove('stat-updated');
                        }, 2000);
                        
                        // Animate to new value
                        animateNumberChange(element, oldValue, newValue);
                    }
                }
            });
        })
        .catch(error => {
            console.error('Failed to update stats:', error);
        });
}

/**
 * Animate Number Change
 */
function animateNumberChange(element, fromValue, toValue) {
    const duration = 1000;
    const startTime = Date.now();
    
    function updateNumber() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(fromValue + (toValue - fromValue) * easeOutQuart);
        
        element.textContent = currentValue.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * Update Recent Activity
 */
function updateRecentActivity() {
    // Update recent users
    fetch('../../api/recent-users.php')
        .then(response => response.text())
        .then(html => {
            const recentUsersTable = document.querySelector('.card:has(.card-title:contains("Recent User Registrations")) .table-responsive');
            if (recentUsersTable) {
                recentUsersTable.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Failed to update recent users:', error);
        });
    
    // Update recent transactions
    fetch('../../api/recent-transactions.php')
        .then(response => response.text())
        .then(html => {
            const recentTransactionsTable = document.querySelector('.card:has(.card-title:contains("Recent Transactions")) .table-responsive');
            if (recentTransactionsTable) {
                recentTransactionsTable.innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Failed to update recent transactions:', error);
        });
}

/**
 * Initialize Stat Tooltips
 */
function initializeStatTooltips() {
    const statCards = document.querySelectorAll('.admin-stat-card');
    
    statCards.forEach(card => {
        const statElement = card.querySelector('[data-stat]');
        if (statElement) {
            const statType = statElement.getAttribute('data-stat');
            let tooltipText = '';
            
            switch (statType) {
                case 'total_users':
                    tooltipText = 'Total number of registered users in the system';
                    break;
                case 'active_users':
                    tooltipText = 'Users with active account status';
                    break;
                case 'today_transactions':
                    tooltipText = 'Number of transactions completed today';
                    break;
                case 'pending_kyc':
                    tooltipText = 'Users awaiting KYC verification';
                    break;
            }
            
            if (tooltipText) {
                card.setAttribute('title', tooltipText);
                card.setAttribute('data-bs-toggle', 'tooltip');
                card.setAttribute('data-bs-placement', 'top');
            }
        }
    });
    
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Update Last Refresh Time
 */
function updateLastRefreshTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    
    let refreshElement = document.querySelector('.last-refresh-time');
    if (!refreshElement) {
        refreshElement = document.createElement('div');
        refreshElement.className = 'last-refresh-time text-muted small';
        refreshElement.style.cssText = 'position: fixed; bottom: 20px; right: 20px; background: rgba(0,0,0,0.7); color: white; padding: 0.5rem 1rem; border-radius: 20px; z-index: 1000;';
        document.body.appendChild(refreshElement);
    }
    
    refreshElement.textContent = `Last updated: ${timeString}`;
}

/**
 * Add CSS animations
 */
const style = document.createElement('style');
style.textContent = `
    @keyframes fade-in-up {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
    
    .fade-in-up {
        animation: fade-in-up 0.6s ease-out forwards;
        opacity: 0;
    }
    
    .stat-updated {
        animation: pulse 2s ease-in-out;
    }
    
    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
    }
    
    .admin-stat-card {
        transition: all 0.3s ease;
    }
    
    .quick-action-item {
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);

// Export functions for global access
window.Dashboard = {
    updateDashboardStats,
    updateRecentActivity,
    animateNumber,
    createRippleEffect
};
