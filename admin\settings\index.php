<?php
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../login.php');
}

$page_title = 'System Settings';

$errors = [];
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $db = getDB();
        
        if ($action === 'appearance') {
            // Handle appearance settings
            $theme = sanitizeInput($_POST['theme'] ?? 'light');
            $color_scheme = sanitizeInput($_POST['color_scheme'] ?? 'blue');
            $sidebar_collapsed = isset($_POST['sidebar_collapsed']) ? 'true' : 'false';
            $header_fixed = isset($_POST['header_fixed']) ? 'true' : 'false';
            $animations_enabled = isset($_POST['animations_enabled']) ? 'true' : 'false';
            
            // Update appearance settings
            $settings = [
                'theme' => $theme,
                'color_scheme' => $color_scheme,
                'sidebar_collapsed' => $sidebar_collapsed,
                'header_fixed' => $header_fixed,
                'animations_enabled' => $animations_enabled
            ];
            
            foreach ($settings as $key => $value) {
                $db->query(
                    "INSERT INTO system_settings (setting_key, setting_value, description, updated_by) 
                     VALUES (?, ?, ?, ?) 
                     ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?",
                    [$key, $value, "Appearance setting: $key", $_SESSION['user_id'], $value, $_SESSION['user_id']]
                );
            }
            
            $success = 'Appearance settings updated successfully!';
            
        } elseif ($action === 'system') {
            // Handle system settings
            $bank_name = sanitizeInput($_POST['bank_name'] ?? '');
            $currency = sanitizeInput($_POST['currency'] ?? 'USD');
            $min_transfer = floatval($_POST['min_transfer_amount'] ?? 1);
            $max_transfer = floatval($_POST['max_transfer_amount'] ?? 10000);
            $daily_limit = floatval($_POST['daily_transfer_limit'] ?? 25000);
            $maintenance_mode = isset($_POST['maintenance_mode']) ? 'true' : 'false';
            $registration_enabled = isset($_POST['registration_enabled']) ? 'true' : 'false';
            
            $system_settings = [
                'bank_name' => $bank_name,
                'currency' => $currency,
                'min_transfer_amount' => $min_transfer,
                'max_transfer_amount' => $max_transfer,
                'daily_transfer_limit' => $daily_limit,
                'maintenance_mode' => $maintenance_mode,
                'registration_enabled' => $registration_enabled
            ];
            
            foreach ($system_settings as $key => $value) {
                $db->query(
                    "INSERT INTO system_settings (setting_key, setting_value, description, updated_by) 
                     VALUES (?, ?, ?, ?) 
                     ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?",
                    [$key, $value, "System setting: $key", $_SESSION['user_id'], $value, $_SESSION['user_id']]
                );
            }
            
            $success = 'System settings updated successfully!';
        }
        
        // Log the activity
        logActivity($_SESSION['user_id'], "Admin updated $action settings", 'system_settings');
        
    } catch (Exception $e) {
        error_log("Settings update error: " . $e->getMessage());
        $errors[] = 'Failed to update settings. Please try again.';
    }
}

// Get current settings
try {
    $db = getDB();
    $settings_result = $db->query("SELECT setting_key, setting_value FROM system_settings");
    $current_settings = [];
    
    while ($row = $settings_result->fetch_assoc()) {
        $current_settings[$row['setting_key']] = $row['setting_value'];
    }
    
} catch (Exception $e) {
    error_log("Settings fetch error: " . $e->getMessage());
    $current_settings = [];
}

include '../../includes/header.php';
?>

<?php include '../../includes/sidebar.php'; ?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Settings</li>
                        </ol>
                    </nav>
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        System Settings
                    </h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger" role="alert">
                <div class="d-flex">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <circle cx="12" cy="12" r="9"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="alert-title">Error!</h4>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
            <div class="alert alert-success" role="alert">
                <div class="d-flex">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l5 5l10 -10"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="alert-title">Success!</h4>
                        <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="row row-cards">
                <!-- Appearance Settings -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="3"/>
                                    <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-3.5L19 4l-2.5 2.5M6.5 17.5L4 20l2.5-2.5m0-11L4 4l2.5 2.5m11 11l2.5 2.5L19 20"/>
                                </svg>
                                Appearance Settings
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="appearance">
                                
                                <div class="mb-3">
                                    <label class="form-label">Theme</label>
                                    <div class="form-selectgroup">
                                        <label class="form-selectgroup-item">
                                            <input type="radio" name="theme" value="light" class="form-selectgroup-input" <?php echo ($current_settings['theme'] ?? 'light') === 'light' ? 'checked' : ''; ?>>
                                            <span class="form-selectgroup-label">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="4"/>
                                                    <path d="M12 2v2m0 16v2m10-10h-2M4 12H2m15.09-5.09l-1.41 1.41M6.32 6.32L4.91 4.91m12.77 12.77l-1.41 1.41M6.32 17.68l-1.41 1.41"/>
                                                </svg>
                                                Light
                                            </span>
                                        </label>
                                        <label class="form-selectgroup-item">
                                            <input type="radio" name="theme" value="dark" class="form-selectgroup-input" <?php echo ($current_settings['theme'] ?? 'light') === 'dark' ? 'checked' : ''; ?>>
                                            <span class="form-selectgroup-label">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M21 12.79A9 9 0 1111.21 3 7 7 0 0021 12.79z"/>
                                                </svg>
                                                Dark
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Color Scheme</label>
                                    <select name="color_scheme" class="form-select">
                                        <option value="blue" <?php echo ($current_settings['color_scheme'] ?? 'blue') === 'blue' ? 'selected' : ''; ?>>Blue</option>
                                        <option value="green" <?php echo ($current_settings['color_scheme'] ?? 'blue') === 'green' ? 'selected' : ''; ?>>Green</option>
                                        <option value="purple" <?php echo ($current_settings['color_scheme'] ?? 'blue') === 'purple' ? 'selected' : ''; ?>>Purple</option>
                                        <option value="red" <?php echo ($current_settings['color_scheme'] ?? 'blue') === 'red' ? 'selected' : ''; ?>>Red</option>
                                        <option value="orange" <?php echo ($current_settings['color_scheme'] ?? 'blue') === 'orange' ? 'selected' : ''; ?>>Orange</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="sidebar_collapsed" <?php echo ($current_settings['sidebar_collapsed'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                        <span class="form-check-label">Collapse sidebar by default</span>
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="header_fixed" <?php echo ($current_settings['header_fixed'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                        <span class="form-check-label">Fixed header</span>
                                    </label>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="animations_enabled" <?php echo ($current_settings['animations_enabled'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                                        <span class="form-check-label">Enable animations</span>
                                    </label>
                                </div>
                                
                                <div class="form-footer">
                                    <button type="submit" class="btn btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                                            <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                                            <path d="M16 5l3 3"/>
                                        </svg>
                                        Update Appearance
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- System Settings -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                                System Settings
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="system">
                                
                                <div class="mb-3">
                                    <label class="form-label">Bank Name</label>
                                    <input type="text" name="bank_name" class="form-control" value="<?php echo htmlspecialchars($current_settings['bank_name'] ?? 'SecureBank Online'); ?>" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Default Currency</label>
                                    <select name="currency" class="form-select">
                                        <option value="USD" <?php echo ($current_settings['currency'] ?? 'USD') === 'USD' ? 'selected' : ''; ?>>USD - US Dollar</option>
                                        <option value="EUR" <?php echo ($current_settings['currency'] ?? 'USD') === 'EUR' ? 'selected' : ''; ?>>EUR - Euro</option>
                                        <option value="GBP" <?php echo ($current_settings['currency'] ?? 'USD') === 'GBP' ? 'selected' : ''; ?>>GBP - British Pound</option>
                                        <option value="CAD" <?php echo ($current_settings['currency'] ?? 'USD') === 'CAD' ? 'selected' : ''; ?>>CAD - Canadian Dollar</option>
                                    </select>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Min Transfer Amount</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" name="min_transfer_amount" class="form-control" step="0.01" min="0" value="<?php echo htmlspecialchars($current_settings['min_transfer_amount'] ?? '1.00'); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Max Transfer Amount</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" name="max_transfer_amount" class="form-control" step="0.01" min="0" value="<?php echo htmlspecialchars($current_settings['max_transfer_amount'] ?? '10000.00'); ?>">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Daily Transfer Limit</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" name="daily_transfer_limit" class="form-control" step="0.01" min="0" value="<?php echo htmlspecialchars($current_settings['daily_transfer_limit'] ?? '25000.00'); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="maintenance_mode" <?php echo ($current_settings['maintenance_mode'] ?? 'false') === 'true' ? 'checked' : ''; ?>>
                                        <span class="form-check-label">Maintenance Mode</span>
                                    </label>
                                    <small class="form-hint">When enabled, only admins can access the system</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="registration_enabled" <?php echo ($current_settings['registration_enabled'] ?? 'true') === 'true' ? 'checked' : ''; ?>>
                                        <span class="form-check-label">User Registration</span>
                                    </label>
                                    <small class="form-hint">Allow new users to register accounts</small>
                                </div>
                                
                                <div class="form-footer">
                                    <button type="submit" class="btn btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                                            <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                                            <path d="M16 5l3 3"/>
                                        </svg>
                                        Update System Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include admin-specific assets -->
<link rel="stylesheet" href="../../assets/admin/admin.css">
<link rel="stylesheet" href="style.css">
<script src="../../assets/admin/admin.js"></script>
<script src="script.js"></script>

<?php include '../../includes/footer.php'; ?>
