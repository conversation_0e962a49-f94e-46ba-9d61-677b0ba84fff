/**
 * Admin Panel JavaScript Functions
 * Handles admin-specific functionality and interactions
 */

// Admin Panel Initialization
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPanel();
    initializeUserManagement();
    initializeAdminForms();
    initializeAdminCharts();
    initializeAppearanceSettings();
});

/**
 * Initialize Admin Panel
 */
function initializeAdminPanel() {
    // Add loading states to admin buttons
    const adminButtons = document.querySelectorAll('.btn-admin-primary, .btn-admin-danger');
    adminButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.type === 'submit') {
                setButtonLoading(this, true);
            }
        });
    });

    // Initialize admin tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-refresh admin statistics every 30 seconds
    if (document.querySelector('.admin-stat-card')) {
        setInterval(refreshAdminStats, 30000);
    }
}

/**
 * Initialize User Management Functions
 */
function initializeUserManagement() {
    // User search functionality
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performUserSearch();
            }, 500);
        });
    }

    // User action confirmations
    const suspendLinks = document.querySelectorAll('a[href*="suspend-user"]');
    suspendLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            confirmUserAction('suspend', this.href);
        });
    });

    const activateLinks = document.querySelectorAll('a[href*="activate-user"]');
    activateLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            confirmUserAction('activate', this.href);
        });
    });

    // Bulk user actions
    initializeBulkActions();
}

/**
 * Initialize Admin Forms
 */
function initializeAdminForms() {
    // Form validation
    const adminForms = document.querySelectorAll('.admin-form form');
    adminForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateAdminForm(this)) {
                e.preventDefault();
            } else {
                setFormLoading(this, true);
            }
        });
    });

    // Password strength indicator
    const passwordInputs = document.querySelectorAll('input[type="password"]');
    passwordInputs.forEach(input => {
        if (input.name === 'password' || input.name === 'new_password') {
            input.addEventListener('input', function() {
                updatePasswordStrength(this);
            });
        }
    });

    // Auto-generate account numbers
    const generateAccountBtn = document.querySelector('#generate-account-number');
    if (generateAccountBtn) {
        generateAccountBtn.addEventListener('click', generateAccountNumber);
    }
}

/**
 * Initialize Admin Charts and Statistics
 */
function initializeAdminCharts() {
    // Initialize Chart.js charts if available
    if (typeof Chart !== 'undefined') {
        initializeUserGrowthChart();
        initializeTransactionChart();
        initializeRevenueChart();
    }
}

/**
 * Initialize Appearance Settings
 */
function initializeAppearanceSettings() {
    // Theme switcher
    const themeToggle = document.querySelector('#theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('change', function() {
            toggleTheme(this.checked ? 'dark' : 'light');
        });
    }

    // Color scheme selector
    const colorSchemeSelect = document.querySelector('#color-scheme');
    if (colorSchemeSelect) {
        colorSchemeSelect.addEventListener('change', function() {
            changeColorScheme(this.value);
        });
    }

    // Sidebar toggle
    const sidebarToggle = document.querySelector('#sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Load saved appearance settings
    loadAppearanceSettings();
}

/**
 * User Management Functions
 */
function performUserSearch() {
    const searchForm = document.querySelector('.admin-search form');
    if (searchForm) {
        const formData = new FormData(searchForm);
        const params = new URLSearchParams(formData);
        
        // Show loading indicator
        showSearchLoading(true);
        
        // Perform AJAX search
        fetch(`users.php?${params.toString()}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newTable = doc.querySelector('.user-table');
            
            if (newTable) {
                document.querySelector('.user-table').innerHTML = newTable.innerHTML;
                initializeUserManagement(); // Re-initialize event listeners
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            showNotification('Search failed. Please try again.', 'error');
        })
        .finally(() => {
            showSearchLoading(false);
        });
    }
}

function confirmUserAction(action, url) {
    const actionText = action === 'suspend' ? 'suspend' : 'activate';
    const message = `Are you sure you want to ${actionText} this user?`;
    
    if (confirm(message)) {
        window.location.href = url;
    }
}

function initializeBulkActions() {
    const selectAllCheckbox = document.querySelector('#select-all-users');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const bulkActionBtn = document.querySelector('#bulk-action-btn');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            userCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionButton();
        });
    }
    
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButton);
    });
}

function updateBulkActionButton() {
    const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
    const bulkActionBtn = document.querySelector('#bulk-action-btn');
    
    if (bulkActionBtn) {
        if (selectedUsers.length > 0) {
            bulkActionBtn.style.display = 'block';
            bulkActionBtn.textContent = `Actions (${selectedUsers.length} selected)`;
        } else {
            bulkActionBtn.style.display = 'none';
        }
    }
}

/**
 * Form Validation Functions
 */
function validateAdminForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Email validation
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'Please enter a valid email address');
            isValid = false;
        }
    });
    
    // Password confirmation
    const passwordField = form.querySelector('input[name="password"], input[name="new_password"]');
    const confirmField = form.querySelector('input[name="confirm_password"]');
    
    if (passwordField && confirmField && passwordField.value !== confirmField.value) {
        showFieldError(confirmField, 'Passwords do not match');
        isValid = false;
    }
    
    return isValid;
}

function showFieldError(field, message) {
    clearFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback d-block';
    errorDiv.textContent = message;
    
    field.classList.add('is-invalid');
    field.parentNode.appendChild(errorDiv);
}

function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Appearance Settings Functions
 */
function toggleTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('admin-theme', theme);
    
    // Update theme-specific elements
    updateThemeElements(theme);
}

function changeColorScheme(scheme) {
    document.documentElement.setAttribute('data-color-scheme', scheme);
    localStorage.setItem('admin-color-scheme', scheme);
    
    showNotification(`Color scheme changed to ${scheme}`, 'success');
}

function toggleSidebar() {
    const sidebar = document.querySelector('.admin-sidebar');
    const content = document.querySelector('.page-wrapper');
    
    if (sidebar && content) {
        sidebar.classList.toggle('collapsed');
        content.classList.toggle('sidebar-collapsed');
        
        // Save sidebar state
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('admin-sidebar-collapsed', isCollapsed);
    }
}

function loadAppearanceSettings() {
    // Load theme
    const savedTheme = localStorage.getItem('admin-theme') || 'light';
    const themeToggle = document.querySelector('#theme-toggle');
    if (themeToggle) {
        themeToggle.checked = savedTheme === 'dark';
        toggleTheme(savedTheme);
    }
    
    // Load color scheme
    const savedColorScheme = localStorage.getItem('admin-color-scheme') || 'blue';
    const colorSchemeSelect = document.querySelector('#color-scheme');
    if (colorSchemeSelect) {
        colorSchemeSelect.value = savedColorScheme;
        changeColorScheme(savedColorScheme);
    }
    
    // Load sidebar state
    const sidebarCollapsed = localStorage.getItem('admin-sidebar-collapsed') === 'true';
    if (sidebarCollapsed) {
        toggleSidebar();
    }
}

/**
 * Utility Functions
 */
function setButtonLoading(button, loading) {
    if (loading) {
        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Processing...';
    } else {
        button.disabled = false;
        button.innerHTML = button.getAttribute('data-original-text') || 'Submit';
    }
}

function showSearchLoading(loading) {
    const searchBtn = document.querySelector('.admin-search button[type="submit"]');
    if (searchBtn) {
        setButtonLoading(searchBtn, loading);
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

function refreshAdminStats() {
    fetch('api/admin-stats.php')
        .then(response => response.json())
        .then(data => {
            updateStatCards(data);
        })
        .catch(error => {
            console.error('Failed to refresh stats:', error);
        });
}

function updateStatCards(data) {
    Object.keys(data).forEach(key => {
        const statElement = document.querySelector(`[data-stat="${key}"]`);
        if (statElement) {
            statElement.textContent = data[key];
        }
    });
}

function generateAccountNumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
    const accountNumber = '10' + timestamp + random;
    
    const accountInput = document.querySelector('input[name="account_number"]');
    if (accountInput) {
        accountInput.value = accountNumber;
    }
    
    return accountNumber;
}

// Export functions for global access
window.AdminPanel = {
    toggleTheme,
    changeColorScheme,
    toggleSidebar,
    showNotification,
    generateAccountNumber,
    validateAdminForm
};
