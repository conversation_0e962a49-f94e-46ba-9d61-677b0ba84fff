<?php
session_start();
require_once '../config/config.php';

// Redirect if already logged in as admin
if (isset($_SESSION['user_id']) && isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
    header('Location: index.php');
    exit();
}

$page_title = 'Admin Login';
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username)) {
        $errors[] = 'Username is required.';
    }
    
    if (empty($password)) {
        $errors[] = 'Password is required.';
    }
    
    if (empty($errors)) {
        try {
            $db = getDB();
            
            // Check for admin user
            $sql = "SELECT id, username, password, first_name, last_name, is_admin, status 
                    FROM accounts 
                    WHERE username = ? AND is_admin = 1";
            
            $result = $db->query($sql, [$username]);
            
            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();
                
                if ($user['status'] !== 'active') {
                    $errors[] = 'Admin account is not active.';
                } elseif (verifyPassword($password, $user['password'])) {
                    // Successful login
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['first_name'] = $user['first_name'];
                    $_SESSION['last_name'] = $user['last_name'];
                    $_SESSION['account_number'] = '**********'; // Admin account number
                    $_SESSION['is_admin'] = true;
                    
                    // Update last login
                    $db->query("UPDATE accounts SET last_login = NOW() WHERE id = ?", [$user['id']]);
                    
                    // Log the login
                    logActivity($user['id'], 'Admin login', 'accounts', $user['id']);
                    
                    // Redirect to admin dashboard
                    header('Location: index.php');
                    exit();
                } else {
                    $errors[] = 'Invalid username or password.';
                }
            } else {
                $errors[] = 'Invalid username or password.';
            }
            
        } catch (Exception $e) {
            error_log("Admin login error: " . $e->getMessage());
            $errors[] = 'Login failed. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title><?php echo htmlspecialchars($page_title); ?> - SecureBank Online</title>
    <!-- CSS files -->
    <link href="../assets/css/tabler.min.css" rel="stylesheet"/>
    <link href="../assets/css/tabler-flags.min.css" rel="stylesheet"/>
    <link href="../assets/css/tabler-payments.min.css" rel="stylesheet"/>
    <link href="../assets/css/tabler-vendors.min.css" rel="stylesheet"/>
    <link href="../assets/css/demo.min.css" rel="stylesheet"/>
    <link href="../assets/admin/admin.css" rel="stylesheet"/>
    <style>
        .admin-login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .admin-login-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .admin-login-header {
            background: linear-gradient(135deg, #1e293b, #334155);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .admin-login-header h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .admin-login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.8;
            font-size: 0.875rem;
        }
        
        .admin-login-body {
            padding: 2rem;
        }
        
        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        }
        
        .btn-admin-login {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-admin-login:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            color: white;
        }
        
        .admin-login-footer {
            background: #f8fafc;
            padding: 1rem 2rem;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }
        
        .admin-login-footer a {
            color: #64748b;
            text-decoration: none;
            font-size: 0.875rem;
        }
        
        .admin-login-footer a:hover {
            color: #3b82f6;
        }
    </style>
</head>
<body class="admin-login-page">
    <div class="admin-login-card">
        <div class="admin-login-header">
            <div class="mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg" width="48" height="48" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                    <rect x="3" y="11" width="18" height="10" rx="2"/>
                    <circle cx="12" cy="16" r="1"/>
                    <path d="M7 11v-4a5 5 0 0 1 10 0v4"/>
                </svg>
            </div>
            <h1>Admin Login</h1>
            <p>SecureBank Online Administration</p>
        </div>
        
        <div class="admin-login-body">
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger" role="alert">
                <div class="d-flex">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <circle cx="12" cy="12" r="9"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="alert-title">Login Failed</h4>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="mb-3">
                    <label class="form-label">Admin Username</label>
                    <input type="text" name="username" class="form-control" placeholder="Enter admin username" value="<?php echo htmlspecialchars($username ?? ''); ?>" required autofocus>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input type="password" name="password" class="form-control" placeholder="Enter password" required>
                </div>
                
                <div class="form-footer">
                    <button type="submit" class="btn btn-admin-login">
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"/>
                            <path d="M20 12h-13l3 -3m0 6l-3 -3"/>
                        </svg>
                        Sign In to Admin Panel
                    </button>
                </div>
            </form>
        </div>
        
        <div class="admin-login-footer">
            <a href="../">← Back to Main Site</a>
        </div>
    </div>

    <!-- Tabler Core -->
    <script src="../assets/js/tabler.min.js"></script>
    <script src="../assets/admin/admin.js"></script>
</body>
</html>
